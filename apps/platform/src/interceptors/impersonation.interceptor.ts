import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Inject,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthenticationGrpcClient } from "@repo/nestjs-commons/guards";
import { ALLOW_IMPERSONATION_KEY } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import { UserType } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { Observable } from "rxjs";
import { TeamsService } from "../teams/services/teams.service";
import { UsersService } from "../users/services/users.service";

interface CurrentUser {
  uid: string;
  sub: string;
  email: string;
  userType: UserType;
  orgId: string;
  orgUid: string;
  orgTier: string;
  authId: string;
  userName: string;
  scopes: string[];
  metadata: {
    userType: UserType;
    timezone: string;
  };
}

interface EnhancedRequest extends FastifyRequest {
  user: CurrentUser;
  impersonated<PERSON>ser?: CurrentUser;
}

@Injectable()
export class ImpersonationInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly authClient: AuthenticationGrpcClient,
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    // Only apply to HTTP requests
    if (context.getType() !== "http") {
      return next.handle();
    }

    // Check if the method allows impersonation
    const allowImpersonation = this.reflector.getAllAndOverride<boolean>(
      ALLOW_IMPERSONATION_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!allowImpersonation) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<EnhancedRequest>();
    const userIdHeader = request.headers["x-user-id"] as string;

    // If no X-user-ID header, continue normally
    if (!userIdHeader) {
      return next.handle();
    }

    try {
      // Validate that the current user is a bot and the API key belongs to a privileged app
      const currentUser = request.user;
      if (!currentUser || currentUser.userType !== UserType.BOT_USER) {
        this.logger.warn("Impersonation attempted by non-bot user", {
          userId: currentUser?.uid,
          userType: currentUser?.userType,
        });
        return next.handle();
      }

      // Get API key from request headers
      const apiKey = request.headers["x-api-key"] as string;
      if (!apiKey) {
        this.logger.warn("Impersonation attempted without API key");
        return next.handle();
      }

      // Validate API key and check if it belongs to a privileged app
      const apiKeyValidation = await this.authClient.validateKey(apiKey);

      // For now, we'll assume the app is privileged if the API key is valid
      // TODO: Add actual privileged app validation via gRPC
      if (!apiKeyValidation) {
        this.logger.warn("Impersonation attempted with invalid API key");
        return next.handle();
      }

      // Validate and fetch the impersonated user
      const impersonatedUser = await this.validateAndFetchUser(
        userIdHeader,
        currentUser.orgId,
      );

      if (impersonatedUser) {
        // Add impersonated user to request
        request.impersonatedUser = impersonatedUser;
        this.logger.info("User impersonation successful", {
          botUserId: currentUser.uid,
          impersonatedUserId: impersonatedUser.uid,
          impersonatedUserEmail: impersonatedUser.email,
        });
      }
    } catch (error) {
      this.logger.error("Error during impersonation validation", {
        error: error.message,
        userIdHeader,
        stack: error.stack,
      });
      // Continue with original user on error
    }

    return next.handle();
  }

  private async validateAndFetchUser(
    userId: string,
    orgId: string,
  ): Promise<CurrentUser | null> {
    try {
      // Fetch user from database using the correct method
      const user = await this.usersService.findOneByPublicId(userId, orgId);

      if (!user) {
        this.logger.warn("Impersonation failed: User not found", {
          userId,
          orgId,
        });
        return null;
      }

      // Check if user is active
      if (!user.isActive) {
        this.logger.warn("Impersonation failed: User is inactive", {
          userId,
          orgId,
        });
        return null;
      }

      // Don't allow impersonating bot users
      if (user.userType === UserType.BOT_USER) {
        this.logger.warn("Impersonation failed: Cannot impersonate bot users", {
          userId,
          orgId,
        });
        return null;
      }

      // Validate user has team access (basic check - user exists in org)
      // More specific team validation can be added based on the entity being acted upon

      // Convert to CurrentUser format
      const currentUser: CurrentUser = {
        uid: user.uid,
        sub: user.authId,
        email: user.email,
        userType: user.userType,
        orgId: user.organizationId,
        orgUid: user.organization?.uid || "",
        orgTier: user.organization?.tier || "FREE",
        authId: user.authId,
        userName: user.email.split("@")[0],
        scopes: [], // Bot users typically don't have scopes
        metadata: {
          userType: user.userType,
          timezone: user.timezone || "UTC",
        },
      };

      return currentUser;
    } catch (error) {
      this.logger.error("Error validating impersonated user", {
        error: error.message,
        userId,
        orgId,
        stack: error.stack,
      });
      return null;
    }
  }
}
