// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v5.29.3
// source: email/email.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.email.v1";

/** Enum for sender's preferred choice */
export enum SendersPreferredChoice {
  UNSPECIFIED = 0,
  FORWARDING_EMAIL = 1,
  CUSTOM_EMAIL = 2,
  UNRECOGNIZED = -1,
}

/** Header represents an email header */
export interface Header {
  name: string;
  value: string;
}

/** KeyValue pair for metadata fields */
export interface MetadataField {
  key: string;
  value: string;
}

/** EmailConfig represents the configuration for email settings */
export interface EmailConfig {
  /** Unique identifier for the config */
  id: string;
  /** Unique identifier */
  uid: string;
  /** User ID */
  userId: string;
  /** Team ID */
  teamId: string;
  /** Organization ID */
  organizationId: string;
  /** Forwarding email address */
  forwardingEmailAddress: string;
  /** Forwarding verification code */
  forwardingVerificationCode: string;
  /** Custom email address */
  customEmail: string;
  /** Domain name */
  domain: string;
  /** Whether email forwarding is verified */
  isEmailForwardingVerified: boolean;
  /** Sender's preferred choice */
  sendersPreferredChoice: SendersPreferredChoice;
  /** Value for sender's preferred choice */
  sendersPreferredChoiceValue: string;
  /** Deletion timestamp */
  deletedAt: string;
  /** Last verification timestamp */
  lastVerifiedAt: string;
  /** Creation timestamp */
  createdAt: string;
  /** Last update timestamp */
  updatedAt: string;
}

/** Request message for sending an email */
export interface SendEmailRequest {
  /** Email address of the sender */
  from: string;
  /** Recipient email address(es) */
  to: string;
  /** Subject line of the email */
  subject?:
    | string
    | undefined;
  /** HTML content of the email */
  htmlBody?:
    | string
    | undefined;
  /** Plain text content of the email */
  textBody?:
    | string
    | undefined;
  /** Optional CC recipients */
  cc?:
    | string
    | undefined;
  /** Optional BCC recipients */
  bcc?: string | undefined;
}

/** Response message after sending an email */
export interface SendEmailResponse {
  to: string;
  cc: string;
  bcc: string;
  submittedAt: string;
  messageId: string;
  errorCode: number;
  message: string;
}

/** Request message for getting email config by ID */
export interface GetEmailConfigByIdRequest {
  /** ID of the email config to retrieve */
  id: string;
}

export const GRPC_EMAIL_V1_PACKAGE_NAME = "grpc.email.v1";

/** EmailProvider service for email operations */

export interface EmailProviderClient {
  /** Send an email */

  sendEmail(request: SendEmailRequest): Observable<SendEmailResponse>;

  /** Get email config by ID */

  getEmailConfigById(request: GetEmailConfigByIdRequest): Observable<EmailConfig>;
}

/** EmailProvider service for email operations */

export interface EmailProviderController {
  /** Send an email */

  sendEmail(request: SendEmailRequest): Promise<SendEmailResponse> | Observable<SendEmailResponse> | SendEmailResponse;

  /** Get email config by ID */

  getEmailConfigById(request: GetEmailConfigByIdRequest): Promise<EmailConfig> | Observable<EmailConfig> | EmailConfig;
}

export function EmailProviderControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["sendEmail", "getEmailConfigById"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("EmailProvider", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("EmailProvider", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const EMAIL_PROVIDER_SERVICE_NAME = "EmailProvider";
