// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v5.29.3
// source: activities/activities.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.activities.v1";

export interface RecordAuditLogRequest {
  organizationId: string;
  activityPerformedById?: string | undefined;
  entityId?: string | undefined;
  entityUid: string;
  entityType: string;
  op: string;
  visibility: string;
  activity: string;
  description: string;
  teamId?: string | undefined;
}

export interface RecordAuditLogResponse {
  success: boolean;
}

export const GRPC_ACTIVITIES_V1_PACKAGE_NAME = "grpc.activities.v1";

export interface ActivitiesClient {
  recordAuditLog(request: RecordAuditLogRequest): Observable<RecordAuditLogResponse>;
}

export interface ActivitiesController {
  recordAuditLog(
    request: RecordAuditLogRequest,
  ): Promise<RecordAuditLogResponse> | Observable<RecordAuditLogResponse> | RecordAuditLogResponse;
}

export function ActivitiesControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["recordAuditLog"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Activities", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Activities", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ACTIVITIES_SERVICE_NAME = "Activities";
