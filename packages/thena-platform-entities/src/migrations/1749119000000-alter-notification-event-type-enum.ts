import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterNotificationEventTypeEnum1749119000000 implements MigrationInterface {
  name = "AlterNotificationEventTypeEnum1749119000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new enum value to the notification_event_type enum
    await queryRunner.query(
      `ALTER TYPE "public"."user_notification_preferences_event_type_enum" ADD VALUE IF NOT EXISTS 'ticket_unarchived'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // PostgreSQL doesn't support removing enum values directly
    // You would need to create a new type, update the column, and drop the old type
    // This is complex and potentially dangerous, so we're not implementing it
    // If you need to revert, you should handle it manually
  }
}
