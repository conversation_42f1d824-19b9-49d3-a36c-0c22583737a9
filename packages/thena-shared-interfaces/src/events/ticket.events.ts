export enum TicketEvents {
  /**
   * A new ticket was created.
   */
  CREATED = "ticket:created",

  /**
   * A ticket was updated.
   */
  UPDATED = "ticket:updated",

  /**
   * A ticket was archived.
   */
  ARCHIVED = "ticket:archived",

  /**
   * A ticket was unarchived.
   */
  UNARCHIVED = "ticket:unarchived",

  /**
   * A ticket was deleted.
   */
  DELETED = "ticket:deleted",

  /**
   * A ticket was escalated.
   */
  ESCALATED = "ticket:escalated",

  /**
   * A ticket status was changed
   */
  STATUS_CHANGED = "ticket:status:changed",

  /**
   * A ticket is assigned to a user
   */
  ASSIGNED = "ticket:assigned",

  /**
   * A ticket priority was changed
   */
  PRIORITY_CHANGED = "ticket:priority:changed",

  /**
   * A comment is added to a ticket
   */
  COMMENT_ADDED = "ticket:comment:added",

  /**
   * A comment is updated on a ticket
   */
  COMMENT_UPDATED = "ticket:comment:updated",

  /**
   * A reaction is added to a comment
   */
  REACTION_ADDED = "ticket:comment:reaction:added",

  /**
   * A reaction is removed from a comment
   */
  REACTION_REMOVED = "ticket:comment:reaction:removed",

  /**
   * A comment is deleted from a ticket
   */
  COMMENT_DELETED = "ticket:comment:deleted",

  /**
   * A custom field is updated on a ticket
   */
  CUSTOM_FIELD_UPDATED = "ticket:custom_field:updated",

  /**
   * A tag is updated on a ticket
   */
  TAG_UPDATED = "ticket:tag:updated",

  /**
   * A SLA breach is detected on a ticket
   */
  SLA_BREACHED = "ticket:sla:breached",

  /**
   * A SLA breach warning is detected on a ticket
   */
  SLA_BREACH_WARNING = "ticket:sla:breach_warning",
}
