import {
  CanActivate,
  ExecutionContext,
  Injectable,
  SetMetadata,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";

export enum OrganizationTier {
  FREE = "FREE",
  STANDARD = "STANDARD",
  ENTERPRISE = "ENTERPRISE",
}

export const REQUIRED_TIER_KEY = "required_tier";
export const RequiredTier = (...tiers: OrganizationTier[]) =>
  SetMetadata(REQUIRED_TIER_KEY, tiers);

export const ALLOW_IMPERSONATION_KEY = "allow_impersonation";
export const AllowImpersonation = () =>
  SetMetadata(ALLOW_IMPERSONATION_KEY, true);

@Injectable()
export class OrganizationTierGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // const requiredTiers = this.reflector.getAllAndOverride<OrganizationTier[]>(REQUIRED_TIER_KEY, [
    //   context.getHandler(),
    //   context.getClass(),
    // ]);
    // if (!requiredTiers) {
    //   return true;
    // }
    // const request = context.switchToHttp().getRequest();
    // const user = request.user;
    // if (!user || !user.orgId) {
    //   throw new ForbiddenException("No organization found for user");
    // }
    // if (!requiredTiers.includes(user.orgTier)) {
    //   throw new ForbiddenException(`This endpoint requires organization tier: ${requiredTiers.join(", ")}`);
    // }

    // TODO: Implement this
    if (context) return true;
  }
}
